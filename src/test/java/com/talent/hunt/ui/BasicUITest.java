package com.talent.hunt.ui;

import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

import static org.assertj.core.api.Assertions.assertThat;

@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
@ActiveProfiles("test")
public class BasicUITest extends BaseUITest {

    @Test
    public void shouldLoadLoginPageSuccessfully() {
        // Navigate to login page
        driver.get(baseUrl + "/login");
        
        // Verify page loads
        assertThat(driver.getTitle()).contains("Login");
        
        // Verify form elements exist
        assertThat(driver.findElement(org.openqa.selenium.By.name("username"))).isNotNull();
        assertThat(driver.findElement(org.openqa.selenium.By.name("password"))).isNotNull();
        
        // Verify login button exists
        assertThat(driver.findElement(org.openqa.selenium.By.xpath("//button[@type='submit']"))).isNotNull();
    }

    @Test
    public void shouldLoadRegisterPageSuccessfully() {
        // Navigate to register page
        driver.get(baseUrl + "/register");
        
        // Verify page loads
        assertThat(driver.getTitle()).contains("Register");
        
        // Verify form elements exist
        assertThat(driver.findElement(org.openqa.selenium.By.name("firstName"))).isNotNull();
        assertThat(driver.findElement(org.openqa.selenium.By.name("lastName"))).isNotNull();
        assertThat(driver.findElement(org.openqa.selenium.By.name("username"))).isNotNull();
        assertThat(driver.findElement(org.openqa.selenium.By.name("email"))).isNotNull();
        assertThat(driver.findElement(org.openqa.selenium.By.name("password"))).isNotNull();
        assertThat(driver.findElement(org.openqa.selenium.By.name("confirmPassword"))).isNotNull();
    }

    @Test
    public void shouldPerformBasicLoginFlow() {
        // Navigate to login page
        driver.get(baseUrl + "/login");
        
        // Fill login form
        driver.findElement(org.openqa.selenium.By.name("username")).sendKeys(TEST_USER_USERNAME);
        driver.findElement(org.openqa.selenium.By.name("password")).sendKeys(TEST_USER_PASSWORD);
        
        // Submit form
        driver.findElement(org.openqa.selenium.By.xpath("//button[@type='submit']")).click();
        
        // Wait for redirect and verify we're on dashboard or login page
        waitForPageLoad();
        String currentUrl = driver.getCurrentUrl();
        
        // Should either be on dashboard (success) or login with error (expected for test user)
        assertThat(currentUrl).satisfiesAnyOf(
            url -> assertThat(url).contains("/dashboard"),
            url -> assertThat(url).contains("/login")
        );
    }

    @Test
    public void shouldNavigateToRegisterFromLogin() {
        // Navigate to login page
        driver.get(baseUrl + "/login");
        
        // Click register link
        driver.findElement(org.openqa.selenium.By.linkText("Register here")).click();
        
        // Wait for page load
        waitForPageLoad();
        
        // Verify we're on register page
        assertThat(driver.getCurrentUrl()).contains("/register");
        assertThat(driver.getTitle()).contains("Register");
    }

    @Test
    public void shouldNavigateToLoginFromRegister() {
        // Navigate to register page
        driver.get(baseUrl + "/register");
        
        // Click login link
        driver.findElement(org.openqa.selenium.By.linkText("Login here")).click();
        
        // Wait for page load
        waitForPageLoad();
        
        // Verify we're on login page
        assertThat(driver.getCurrentUrl()).contains("/login");
        assertThat(driver.getTitle()).contains("Login");
    }
}
