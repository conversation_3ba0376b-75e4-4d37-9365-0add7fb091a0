package com.talent.hunt.ui;

import org.junit.jupiter.api.Test;
import org.openqa.selenium.By;
import org.openqa.selenium.WebElement;

import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;

class DashboardNavigationUITest extends BaseUITest {

    @Test
    void shouldDisplayUserDashboardCorrectly() {
        // Given
        loginAsUser();

        // When
        navigateToDashboard();

        // Then
        assertThat(getPageTitle()).contains("Dashboard");
        assertThat(isTextPresent("Welcome")).isTrue();
        assertThat(isTextPresent(TEST_USER_FIRST_NAME)).isTrue();
        
        // Should show available tests section
        assertThat(isTextPresent("Available Tests") || isTextPresent("Tests")).isTrue();
        
        // Should show user navigation elements
        assertThat(isElementPresent(By.linkText("Dashboard"))).isTrue();
        assertThat(isElementPresent(By.linkText("Logout"))).isTrue();
    }

    @Test
    void shouldDisplayAdminDashboardCorrectly() {
        // Given
        loginAsAdmin();

        // When
        navigateToAdminDashboard();

        // Then
        assertThat(getPageTitle().contains("Admin") || getPageTitle().contains("Dashboard")).isTrue();
        assertThat(isTextPresent("Admin Dashboard") || isTextPresent("Administration")).isTrue();
        assertThat(isTextPresent(TEST_ADMIN_FIRST_NAME)).isTrue();
        
        // Should show admin-specific elements
        assertThat(isTextPresent("Total Users") || isTextPresent("Users")).isTrue();
        assertThat(isTextPresent("Tests") || isTextPresent("All Tests")).isTrue();
        
        // Should show admin navigation elements
        assertThat(isElementPresent(By.linkText("Dashboard")) || isElementPresent(By.linkText("Admin Dashboard"))).isTrue();
        assertThat(isElementPresent(By.linkText("Logout"))).isTrue();
    }

    @Test
    void shouldNavigateToTestCreationFromAdminDashboard() {
        // Given
        loginAsAdmin();
        navigateToAdminDashboard();

        // When - look for test creation link/button
        if (isElementPresent(By.linkText("Create Test"))) {
            clickLink("Create Test");
        } else if (isElementPresent(By.linkText("New Test"))) {
            clickLink("New Test");
        } else if (isElementPresent(By.xpath("//a[contains(text(), 'Test')]"))) {
            WebElement testLink = driver.findElement(By.xpath("//a[contains(text(), 'Test')]"));
            testLink.click();
        }

        // Then
        waitForPageLoad();
        assertThat(getCurrentUrl().contains("/test") || getCurrentUrl().contains("/create")).isTrue();
    }

    @Test
    void shouldShowUserSessionHistory() {
        // Given
        loginAsUser();

        // When
        navigateToDashboard();

        // Then - should show user's test sessions (even if empty)
        assertThat(isTextPresent("Your Test Sessions") || 
                  isTextPresent("Test History") || 
                  isTextPresent("Sessions") ||
                  isTextPresent("No tests taken yet")).isTrue();
    }

    @Test
    void shouldShowActiveSessionIfExists() {
        // Given
        loginAsUser();

        // When
        navigateToDashboard();

        // Then - should show active session section (even if no active session)
        assertThat(isTextPresent("Active Session") || 
                  isTextPresent("Continue Test") || 
                  isTextPresent("No active session")).isTrue();
    }

    @Test
    void shouldDisplayAvailableTestsForUser() {
        // Given
        loginAsUser();

        // When
        navigateToDashboard();

        // Then
        assertThat(isTextPresent("Available Tests") || 
                  isTextPresent("Tests") ||
                  isTextPresent("No tests available")).isTrue();
    }

    @Test
    void shouldShowUserProfileInformation() {
        // Given
        loginAsUser();

        // When
        navigateToDashboard();

        // Then
        assertThat(isTextPresent(TEST_USER_FIRST_NAME)).isTrue();
        assertThat(isTextPresent(TEST_USER_LAST_NAME) || isTextPresent(TEST_USER_USERNAME)).isTrue();
    }

    @Test
    void shouldShowAdminStatistics() {
        // Given
        loginAsAdmin();

        // When
        navigateToAdminDashboard();

        // Then - should show some statistics
        assertThat(isTextPresent("Total Users") || 
                  isTextPresent("Active Users") || 
                  isTextPresent("Tests") ||
                  isTextPresent("Statistics")).isTrue();
    }

    @Test
    void shouldShowRecentSessionsForAdmin() {
        // Given
        loginAsAdmin();

        // When
        navigateToAdminDashboard();

        // Then
        assertThat(isTextPresent("Recent Sessions") || 
                  isTextPresent("Test Sessions") || 
                  isTextPresent("Sessions") ||
                  isTextPresent("No recent sessions")).isTrue();
    }

    @Test
    void shouldHaveWorkingNavigationLinks() {
        // Given
        loginAsUser();

        // When - test dashboard link
        navigateToDashboard();
        if (isElementPresent(By.linkText("Dashboard"))) {
            clickLink("Dashboard");
            waitForPageLoad();
            assertThat(getCurrentUrl()).contains("/dashboard");
        }

        // Test logout link
        if (isElementPresent(By.linkText("Logout"))) {
            clickLink("Logout");
            waitForPageLoad();
            assertThat(getCurrentUrl()).contains("/login");
        }
    }

    @Test
    void shouldShowDifferentContentForUserVsAdmin() {
        // Test user dashboard
        loginAsUser();
        navigateToDashboard();
        String userDashboardContent = driver.getPageSource();
        logout();

        // Test admin dashboard
        loginAsAdmin();
        navigateToAdminDashboard();
        String adminDashboardContent = driver.getPageSource();

        // Then - content should be different
        assertThat(userDashboardContent).isNotEqualTo(adminDashboardContent);
        
        // Admin should have additional features
        assertThat(adminDashboardContent).contains("Admin");
    }

    @Test
    void shouldHandleEmptyStateGracefully() {
        // Given - new user with no test history
        loginAsUser();

        // When
        navigateToDashboard();

        // Then - should handle empty states gracefully
        assertThat(isTextPresent("No tests") || 
                  isTextPresent("No sessions") || 
                  isTextPresent("Get started") ||
                  isTextPresent("Available Tests")).isTrue();
    }

    @Test
    void shouldDisplayPageTitlesCorrectly() {
        // Test login page title
        navigateToLogin();
        assertThat(getPageTitle()).isNotEmpty();

        // Test register page title
        navigateToRegister();
        assertThat(getPageTitle()).isNotEmpty();

        // Test dashboard title
        loginAsUser();
        navigateToDashboard();
        assertThat(getPageTitle()).isNotEmpty();
    }

    @Test
    void shouldHaveResponsiveLayout() {
        // Given
        loginAsUser();
        navigateToDashboard();

        // When - check if basic responsive elements are present
        // This is a basic check - in a real scenario you'd test different viewport sizes
        List<WebElement> containers = driver.findElements(By.cssSelector(".container, .container-fluid, .row, .col"));
        
        // Then - should have some responsive layout elements
        assertThat(containers.size()).isGreaterThan(0);
    }

    @Test
    void shouldShowBreadcrumbsOrNavigationPath() {
        // Given
        loginAsUser();

        // When
        navigateToDashboard();

        // Then - should show some form of navigation context
        assertThat(isTextPresent("Dashboard") || 
                  isTextPresent("Home") || 
                  isElementPresent(By.cssSelector(".breadcrumb, .nav, .navbar"))).isTrue();
    }

    @Test
    void shouldHandleDirectURLAccess() {
        // Given - user is logged in
        loginAsUser();

        // When - directly access dashboard URL
        driver.get(baseUrl + "/dashboard");
        waitForPageLoad();

        // Then - should load correctly
        assertThat(getCurrentUrl()).contains("/dashboard");
        assertThat(isTextPresent("Welcome") || isTextPresent("Dashboard")).isTrue();
    }

    @Test
    void shouldMaintainSessionAcrossPageNavigation() {
        // Given
        loginAsUser();
        String initialUrl = getCurrentUrl();

        // When - navigate to different pages
        navigateToDashboard();
        driver.get(baseUrl + "/dashboard"); // Direct navigation
        waitForPageLoad();

        // Then - should still be logged in
        assertThat(getCurrentUrl()).contains("/dashboard");
        assertThat(isTextPresent(TEST_USER_FIRST_NAME)).isTrue();
        
        // Should not be redirected to login
        assertThat(getCurrentUrl()).doesNotContain("/login");
    }
}
