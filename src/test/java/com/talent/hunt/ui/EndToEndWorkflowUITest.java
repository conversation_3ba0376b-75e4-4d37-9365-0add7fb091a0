package com.talent.hunt.ui;

import org.junit.jupiter.api.Test;
import org.openqa.selenium.By;
import org.openqa.selenium.WebElement;
import org.openqa.selenium.support.ui.Select;

import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;

class EndToEndWorkflowUITest extends BaseUITest {

    @Test
    void shouldCompleteFullUserRegistrationToTestCompletionWorkflow() {
        // Step 1: Register new user
        String uniqueId = String.valueOf(System.currentTimeMillis());
        String username = "e2euser" + uniqueId;
        String email = "e2euser" + uniqueId + "@example.com";
        
        navigateToRegister();
        fillForm("username", username);
        fillForm("email", email);
        fillForm("firstName", "E2E");
        fillForm("lastName", "User");
        fillForm("password", "password123");
        fillForm("confirmPassword", "password123");
        clickButton("Register");
        waitForPageLoad();

        // Step 2: Login with new credentials
        assertThat(getCurrentUrl()).contains("/login");
        fillForm("username", username);
        fillForm("password", "password123");
        clickButton("Login");
        waitForPageLoad();

        // Step 3: Verify dashboard access
        assertThat(getCurrentUrl()).contains("/dashboard");
        assertThat(isTextPresent("Welcome")).isTrue();
        assertThat(isTextPresent("E2E")).isTrue();

        // Step 4: Check available tests
        assertThat(isTextPresent("Available Tests") || 
                  isTextPresent("Tests") ||
                  isTextPresent("No tests available")).isTrue();

        // Step 5: Start a test if available
        if (isElementPresent(By.xpath("//button[contains(text(), 'Start')] | //a[contains(text(), 'Start')]"))) {
            WebElement startButton = driver.findElement(
                By.xpath("//button[contains(text(), 'Start')] | //a[contains(text(), 'Start')]"));
            startButton.click();
            waitForPageLoad();

            // Step 6: Take the test
            if (isTextPresent("Question")) {
                // Answer questions if available
                List<WebElement> answerOptions = driver.findElements(
                    By.cssSelector("input[type='radio'], input[type='checkbox']"));
                
                if (answerOptions.size() > 0) {
                    answerOptions.get(0).click();
                    
                    if (isElementPresent(By.xpath("//button[contains(text(), 'Submit')] | " +
                                                 "//button[contains(text(), 'Next')]"))) {
                        WebElement submitButton = driver.findElement(
                            By.xpath("//button[contains(text(), 'Submit')] | " +
                                    "//button[contains(text(), 'Next')]"));
                        submitButton.click();
                        waitForPageLoad();
                    }
                }
            }
        }

        // Step 7: Logout
        logout();
        assertThat(getCurrentUrl()).contains("/login");
        assertThat(isTextPresent("logged out")).isTrue();
    }

    @Test
    void shouldCompleteFullAdminWorkflowFromTestCreationToUserCompletion() {
        // Step 1: Login as admin
        loginAsAdmin();
        assertThat(getCurrentUrl()).contains("/dashboard");

        // Step 2: Create a new test
        driver.get(baseUrl + "/tests/create");
        waitForPageLoad();

        String testTitle = "E2E Admin Test - " + System.currentTimeMillis();
        fillForm("title", testTitle);
        fillForm("description", "End-to-end test created by admin");
        fillForm("durationMinutes", "30");
        fillForm("passingScore", "60");
        clickButton("Create Test");
        waitForPageLoad();

        // Step 3: Add questions to the test
        if (getCurrentUrl().contains("/questions") || isTextPresent("Add Question")) {
            // Add first question
            fillForm("questionText", "What is the capital of France?");
            
            if (isElementPresent(By.name("questionType"))) {
                Select questionType = new Select(driver.findElement(By.name("questionType")));
                questionType.selectByValue("MULTIPLE_CHOICE");
            }
            
            fillForm("points", "10");
            
            // Add answer options
            if (isElementPresent(By.name("answerOptions[0].optionText"))) {
                fillForm("answerOptions[0].optionText", "London");
                fillForm("answerOptions[1].optionText", "Paris");
                fillForm("answerOptions[2].optionText", "Berlin");
                fillForm("answerOptions[3].optionText", "Madrid");
                
                // Mark correct answer (Paris)
                if (isElementPresent(By.name("answerOptions[1].isCorrect"))) {
                    WebElement correctAnswer = driver.findElement(By.name("answerOptions[1].isCorrect"));
                    correctAnswer.click();
                }
            }
            
            clickButton("Add Question");
            waitForPageLoad();

            // Add second question
            if (isTextPresent("Add Question") || isElementPresent(By.name("questionText"))) {
                fillForm("questionText", "What is 2 + 2?");
                fillForm("points", "5");
                
                if (isElementPresent(By.name("answerOptions[0].optionText"))) {
                    fillForm("answerOptions[0].optionText", "3");
                    fillForm("answerOptions[1].optionText", "4");
                    fillForm("answerOptions[2].optionText", "5");
                    fillForm("answerOptions[3].optionText", "6");
                    
                    // Mark correct answer (4)
                    if (isElementPresent(By.name("answerOptions[1].isCorrect"))) {
                        WebElement correctAnswer = driver.findElement(By.name("answerOptions[1].isCorrect"));
                        correctAnswer.click();
                    }
                }
                
                clickButton("Add Question");
                waitForPageLoad();
            }
        }

        // Step 4: Logout as admin
        logout();

        // Step 5: Login as regular user
        loginAsUser();
        navigateToDashboard();

        // Step 6: Find and start the newly created test
        if (isTextPresent(testTitle) || isElementPresent(By.xpath("//button[contains(text(), 'Start')]"))) {
            WebElement startButton = driver.findElement(
                By.xpath("//button[contains(text(), 'Start')] | //a[contains(text(), 'Start')]"));
            startButton.click();
            waitForPageLoad();

            // Step 7: Take the test
            // Answer first question
            if (isTextPresent("capital of France")) {
                List<WebElement> answerOptions = driver.findElements(
                    By.cssSelector("input[type='radio']"));
                
                // Select Paris (should be option 2)
                if (answerOptions.size() >= 2) {
                    answerOptions.get(1).click(); // Paris
                    
                    if (isElementPresent(By.xpath("//button[contains(text(), 'Submit')] | " +
                                                 "//button[contains(text(), 'Next')]"))) {
                        WebElement submitButton = driver.findElement(
                            By.xpath("//button[contains(text(), 'Submit')] | " +
                                    "//button[contains(text(), 'Next')]"));
                        submitButton.click();
                        waitForPageLoad();
                    }
                }
            }

            // Answer second question
            if (isTextPresent("2 + 2")) {
                List<WebElement> answerOptions = driver.findElements(
                    By.cssSelector("input[type='radio']"));
                
                // Select 4 (should be option 2)
                if (answerOptions.size() >= 2) {
                    answerOptions.get(1).click(); // 4
                    
                    if (isElementPresent(By.xpath("//button[contains(text(), 'Submit')] | " +
                                                 "//button[contains(text(), 'Complete')] | " +
                                                 "//button[contains(text(), 'Finish')]"))) {
                        WebElement submitButton = driver.findElement(
                            By.xpath("//button[contains(text(), 'Submit')] | " +
                                    "//button[contains(text(), 'Complete')] | " +
                                    "//button[contains(text(), 'Finish')]"));
                        submitButton.click();
                        waitForPageLoad();
                    }
                }
            }

            // Step 8: Verify test completion
            assertThat(isTextPresent("completed") || 
                      isTextPresent("submitted") || 
                      isTextPresent("finished") ||
                      isTextPresent("Score")).isTrue();
        }

        // Step 9: Check test results
        navigateToDashboard();
        if (isTextPresent("Test Sessions") || isTextPresent("History")) {
            assertThat(isTextPresent(testTitle) || 
                      isTextPresent("completed") ||
                      isTextPresent("Score")).isTrue();
        }
    }

    @Test
    void shouldHandleCompleteUserJourneyWithValidation() {
        // Step 1: Try to access protected resource without login
        navigateToDashboard();
        assertThat(getCurrentUrl()).contains("/login");

        // Step 2: Try invalid login
        fillForm("username", "invaliduser");
        fillForm("password", "wrongpassword");
        clickButton("Login");
        waitForPageLoad();
        assertThat(getCurrentUrl()).contains("/login");
        assertThat(isTextPresent("Invalid")).isTrue();

        // Step 3: Successful login
        loginAsUser();
        assertThat(getCurrentUrl()).contains("/dashboard");

        // Step 4: Navigate through different sections
        navigateToDashboard();
        assertThat(isTextPresent("Welcome")).isTrue();

        // Step 5: Try to access admin area (should be denied)
        navigateToAdminDashboard();
        assertThat(getCurrentUrl().contains("/admin/dashboard") == false ||
                  isTextPresent("Access Denied") ||
                  isTextPresent("403")).isTrue();

        // Step 6: Return to user dashboard
        navigateToDashboard();
        assertThat(getCurrentUrl()).contains("/dashboard");

        // Step 7: Logout and verify
        logout();
        assertThat(getCurrentUrl()).contains("/login");
    }

    @Test
    void shouldDemonstrateRoleBasedAccessControl() {
        // Step 1: Test admin capabilities
        loginAsAdmin();
        
        // Admin should access admin dashboard
        navigateToAdminDashboard();
        assertThat(getCurrentUrl().contains("/admin") ||
                  isTextPresent("Admin")).isTrue();

        // Admin should access test creation
        driver.get(baseUrl + "/tests/create");
        waitForPageLoad();
        assertThat(getCurrentUrl().contains("/test") ||
                  isTextPresent("Create Test")).isTrue();

        logout();

        // Step 2: Test user limitations
        loginAsUser();
        
        // User should access regular dashboard
        navigateToDashboard();
        assertThat(getCurrentUrl()).contains("/dashboard");
        assertThat(isTextPresent("Welcome")).isTrue();

        // User should NOT access admin dashboard
        navigateToAdminDashboard();
        assertThat(getCurrentUrl().contains("/admin/dashboard") == false ||
                  isTextPresent("Access Denied") ||
                  isTextPresent("403")).isTrue();

        // User should NOT access test creation
        driver.get(baseUrl + "/tests/create");
        waitForPageLoad();
        assertThat(getCurrentUrl().contains("/login") ||
                  isTextPresent("Access Denied") ||
                  isTextPresent("403")).isTrue();
    }

    @Test
    void shouldHandleSessionManagement() {
        // Step 1: Login and establish session
        loginAsUser();
        String sessionUrl = getCurrentUrl();
        assertThat(sessionUrl).contains("/dashboard");

        // Step 2: Navigate to different pages (session should persist)
        driver.get(baseUrl + "/dashboard");
        waitForPageLoad();
        assertThat(getCurrentUrl()).contains("/dashboard");
        assertThat(isTextPresent(TEST_USER_FIRST_NAME)).isTrue();

        // Step 3: Direct URL access should work with valid session
        driver.get(baseUrl + "/dashboard");
        waitForPageLoad();
        assertThat(getCurrentUrl()).contains("/dashboard");

        // Step 4: Logout should invalidate session
        logout();
        assertThat(getCurrentUrl()).contains("/login");

        // Step 5: Try to access protected resource after logout
        driver.get(baseUrl + "/dashboard");
        waitForPageLoad();
        assertThat(getCurrentUrl()).contains("/login");
    }

    @Test
    void shouldValidateFormInputsAcrossWorkflow() {
        // Step 1: Test registration form validation
        navigateToRegister();
        
        // Submit empty form
        clickButton("Register");
        waitForPageLoad();
        assertThat(getCurrentUrl()).contains("/register");
        assertThat(isTextPresent("required") || 
                  isTextPresent("cannot be empty")).isTrue();

        // Test invalid email
        fillForm("username", "testuser");
        fillForm("email", "invalid-email");
        fillForm("firstName", "Test");
        fillForm("lastName", "User");
        fillForm("password", "password123");
        fillForm("confirmPassword", "password123");
        clickButton("Register");
        waitForPageLoad();
        assertThat(getCurrentUrl()).contains("/register");

        // Step 2: Test login form validation
        navigateToLogin();
        clickButton("Login");
        waitForPageLoad();
        // Should either show validation or attempt login with empty fields

        // Step 3: Test admin form validation (if accessible)
        loginAsAdmin();
        driver.get(baseUrl + "/tests/create");
        waitForPageLoad();
        
        if (getCurrentUrl().contains("/test")) {
            // Submit empty test creation form
            clickButton("Create Test");
            waitForPageLoad();
            assertThat(getCurrentUrl()).contains("/test");
            assertThat(isTextPresent("required") || 
                      isTextPresent("cannot be empty")).isTrue();
        }
    }
}
