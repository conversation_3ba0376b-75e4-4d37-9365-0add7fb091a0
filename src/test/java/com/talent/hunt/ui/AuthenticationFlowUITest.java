package com.talent.hunt.ui;

import org.junit.jupiter.api.Test;
import org.openqa.selenium.By;
import org.openqa.selenium.WebElement;

import static org.assertj.core.api.Assertions.assertThat;

class AuthenticationFlowUITest extends BaseUITest {

    @Test
    void shouldDisplayLoginPageCorrectly() {
        // When
        navigateToLogin();

        // Then
        assertThat(getPageTitle()).contains("Login");
        assertThat(isElementPresent(By.name("username"))).isTrue();
        assertThat(isElementPresent(By.name("password"))).isTrue();
        assertThat(isElementPresent(By.cssSelector("button[type='submit']"))).isTrue();
        assertThat(isTextPresent("Login to Talent Hunt")).isTrue();
    }

    @Test
    void shouldDisplayRegistrationPageCorrectly() {
        // When
        navigateToRegister();

        // Then
        assertThat(getPageTitle()).contains("Register");
        assertThat(isElementPresent(By.name("username"))).isTrue();
        assertThat(isElementPresent(By.name("email"))).isTrue();
        assertThat(isElementPresent(By.name("firstName"))).isTrue();
        assertThat(isElementPresent(By.name("lastName"))).isTrue();
        assertThat(isElementPresent(By.name("password"))).isTrue();
        assertThat(isElementPresent(By.name("confirmPassword"))).isTrue();
        assertThat(isElementPresent(By.cssSelector("button[type='submit']"))).isTrue();
    }

    @Test
    void shouldLoginSuccessfullyWithValidCredentials() {
        // When
        loginAsUser();

        // Then
        assertThat(getCurrentUrl()).contains("/dashboard");
        assertThat(isTextPresent("Welcome")).isTrue();
        assertThat(isTextPresent(TEST_USER_FIRST_NAME)).isTrue();
    }

    @Test
    void shouldLoginAsAdminSuccessfully() {
        // When
        loginAsAdmin();

        // Then
        assertThat(getCurrentUrl()).contains("/dashboard");
        assertThat(isTextPresent("Welcome")).isTrue();
        assertThat(isTextPresent(TEST_ADMIN_FIRST_NAME)).isTrue();
        // Admin should have access to admin features
        assertThat(isTextPresent("Admin")).isTrue();
    }

    @Test
    void shouldShowErrorForInvalidCredentials() {
        // Given
        navigateToLogin();

        // When
        fillForm("username", "invaliduser");
        fillForm("password", "wrongpassword");
        clickButton("Login");

        // Then
        waitForPageLoad();
        assertThat(getCurrentUrl()).contains("/login");
        assertThat(isTextPresent("Invalid username or password")).isTrue();
    }

    @Test
    void shouldRegisterNewUserSuccessfully() {
        // Given
        String newUsername = "newuser" + System.currentTimeMillis();
        String newEmail = "newuser" + System.currentTimeMillis() + "@example.com";
        
        navigateToRegister();

        // When
        fillForm("username", newUsername);
        fillForm("email", newEmail);
        fillForm("firstName", "New");
        fillForm("lastName", "User");
        fillForm("password", "newpassword123");
        fillForm("confirmPassword", "newpassword123");
        clickButton("Register");

        // Then
        waitForPageLoad();
        assertThat(getCurrentUrl()).contains("/login");
        assertThat(isTextPresent("Registration successful")).isTrue();

        // Verify can login with new credentials
        fillForm("username", newUsername);
        fillForm("password", "newpassword123");
        clickButton("Login");

        waitForPageLoad();
        assertThat(getCurrentUrl()).contains("/dashboard");
        assertThat(isTextPresent("Welcome")).isTrue();
    }

    @Test
    void shouldShowErrorForDuplicateUsername() {
        // Given
        navigateToRegister();

        // When - try to register with existing username
        fillForm("username", TEST_USER_USERNAME);
        fillForm("email", "<EMAIL>");
        fillForm("firstName", "Different");
        fillForm("lastName", "User");
        fillForm("password", "password123");
        fillForm("confirmPassword", "password123");
        clickButton("Register");

        // Then
        waitForPageLoad();
        assertThat(getCurrentUrl()).contains("/register");
        assertThat(isTextPresent("Username already exists") || isTextPresent("already taken")).isTrue();
    }

    @Test
    void shouldShowErrorForPasswordMismatch() {
        // Given
        navigateToRegister();

        // When
        fillForm("username", "testuser2");
        fillForm("email", "<EMAIL>");
        fillForm("firstName", "Test");
        fillForm("lastName", "User2");
        fillForm("password", "password123");
        fillForm("confirmPassword", "differentpassword");
        clickButton("Register");

        // Then
        waitForPageLoad();
        assertThat(getCurrentUrl()).contains("/register");
        assertThat(isTextPresent("Passwords do not match") || isTextPresent("password") && isTextPresent("match")).isTrue();
    }

    @Test
    void shouldLogoutSuccessfully() {
        // Given - user is logged in
        loginAsUser();
        assertThat(getCurrentUrl()).contains("/dashboard");

        // When
        logout();

        // Then
        assertThat(getCurrentUrl()).contains("/login");
        assertThat(isTextPresent("logged out successfully")).isTrue();
    }

    @Test
    void shouldRedirectToLoginWhenAccessingProtectedPageWithoutAuth() {
        // When - try to access dashboard without login
        navigateToDashboard();

        // Then - should be redirected to login
        assertThat(getCurrentUrl()).contains("/login");
    }

    @Test
    void shouldRedirectToLoginWhenAccessingAdminPageWithoutAuth() {
        // When - try to access admin dashboard without login
        navigateToAdminDashboard();

        // Then - should be redirected to login
        assertThat(getCurrentUrl()).contains("/login");
    }

    @Test
    void shouldDenyAccessToAdminPageForRegularUser() {
        // Given - regular user is logged in
        loginAsUser();

        // When - try to access admin dashboard
        navigateToAdminDashboard();

        // Then - should be denied access (403 or redirect)
        assertThat(getCurrentUrl()).doesNotContain("/admin/dashboard");
        assertThat(isTextPresent("Access Denied") || isTextPresent("403") || getCurrentUrl().contains("/login")).isTrue();
    }

    @Test
    void shouldAllowAccessToAdminPageForAdminUser() {
        // Given - admin user is logged in
        loginAsAdmin();

        // When - access admin dashboard
        navigateToAdminDashboard();

        // Then - should have access
        assertThat(getCurrentUrl()).contains("/admin/dashboard");
        assertThat(isTextPresent("Admin Dashboard") || isTextPresent("Administration")).isTrue();
    }

    @Test
    void shouldValidateRequiredFieldsOnRegistration() {
        // Given
        navigateToRegister();

        // When - submit form without filling required fields
        clickButton("Register");

        // Then - should show validation errors
        waitForPageLoad();
        assertThat(getCurrentUrl()).contains("/register");
        // Check for validation messages (exact text may vary)
        assertThat(isTextPresent("required") || isTextPresent("cannot be empty") || isTextPresent("must not be blank")).isTrue();
    }

    @Test
    void shouldValidateEmailFormat() {
        // Given
        navigateToRegister();

        // When - enter invalid email format
        fillForm("username", "testuser3");
        fillForm("email", "invalid-email");
        fillForm("firstName", "Test");
        fillForm("lastName", "User3");
        fillForm("password", "password123");
        fillForm("confirmPassword", "password123");
        clickButton("Register");

        // Then
        waitForPageLoad();
        assertThat(getCurrentUrl()).contains("/register");
        assertThat(isTextPresent("valid email") || isTextPresent("email format")).isTrue();
    }

    @Test
    void shouldNavigateBetweenLoginAndRegisterPages() {
        // Given - on login page
        navigateToLogin();

        // When - click register link
        clickLink("Register");

        // Then - should be on register page
        waitForPageLoad();
        assertThat(getCurrentUrl()).contains("/register");

        // When - click login link
        clickLink("Login");

        // Then - should be back on login page
        waitForPageLoad();
        assertThat(getCurrentUrl()).contains("/login");
    }
}
