package com.talent.hunt.ui;

import com.talent.hunt.application.service.UserService;
import com.talent.hunt.domain.model.User;
import com.talent.hunt.domain.model.UserRole;
import com.talent.hunt.domain.repository.UserRepository;
import io.github.bonigarcia.wdm.WebDriverManager;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.BeforeEach;
import org.openqa.selenium.By;
import org.openqa.selenium.WebDriver;
import org.openqa.selenium.WebElement;
import org.openqa.selenium.chrome.ChromeDriver;
import org.openqa.selenium.chrome.ChromeOptions;
import org.openqa.selenium.support.ui.WebDriverWait;
import org.openqa.selenium.support.ui.ExpectedConditions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.web.server.LocalServerPort;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.transaction.annotation.Transactional;

import java.time.Duration;

import static org.awaitility.Awaitility.await;

@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
@ActiveProfiles("test")
@Transactional
public abstract class BaseUITest {

    @LocalServerPort
    protected int port;

    protected WebDriver driver;
    protected WebDriverWait wait;
    protected String baseUrl;

    @Autowired
    protected UserRepository userRepository;

    @Autowired
    protected UserService userService;

    @Autowired
    protected PasswordEncoder passwordEncoder;

    // Test user credentials
    protected static final String TEST_USER_USERNAME = "testuser";
    protected static final String TEST_USER_PASSWORD = "password123";
    protected static final String TEST_USER_EMAIL = "<EMAIL>";
    protected static final String TEST_USER_FIRST_NAME = "Test";
    protected static final String TEST_USER_LAST_NAME = "User";

    // Test admin credentials
    protected static final String TEST_ADMIN_USERNAME = "testadmin";
    protected static final String TEST_ADMIN_PASSWORD = "admin123";
    protected static final String TEST_ADMIN_EMAIL = "<EMAIL>";
    protected static final String TEST_ADMIN_FIRST_NAME = "Test";
    protected static final String TEST_ADMIN_LAST_NAME = "Admin";

    @BeforeAll
    static void setupWebDriver() {
        WebDriverManager.chromedriver().setup();
    }

    @BeforeEach
    void setUp() {
        ChromeOptions options = new ChromeOptions();
        options.addArguments("--headless"); // Run in headless mode for CI/CD
        options.addArguments("--no-sandbox");
        options.addArguments("--disable-dev-shm-usage");
        options.addArguments("--disable-gpu");
        options.addArguments("--window-size=1920,1080");

        driver = new ChromeDriver(options);
        wait = new WebDriverWait(driver, Duration.ofSeconds(10));
        baseUrl = "http://localhost:" + port;

        // Create test users
        createTestUsers();
    }

    @AfterEach
    void tearDown() {
        if (driver != null) {
            driver.quit();
        }
    }

    protected void createTestUsers() {
        // Create test user
        User testUser = User.builder()
                .username(TEST_USER_USERNAME)
                .email(TEST_USER_EMAIL)
                .passwordHash(passwordEncoder.encode(TEST_USER_PASSWORD))
                .firstName(TEST_USER_FIRST_NAME)
                .lastName(TEST_USER_LAST_NAME)
                .role(UserRole.USER)
                .isActive(true)
                .build();
        userRepository.save(testUser);

        // Create test admin
        User testAdmin = User.builder()
                .username(TEST_ADMIN_USERNAME)
                .email(TEST_ADMIN_EMAIL)
                .passwordHash(passwordEncoder.encode(TEST_ADMIN_PASSWORD))
                .firstName(TEST_ADMIN_FIRST_NAME)
                .lastName(TEST_ADMIN_LAST_NAME)
                .role(UserRole.ADMIN)
                .isActive(true)
                .build();
        userRepository.save(testAdmin);
    }

    protected void navigateToLogin() {
        driver.get(baseUrl + "/login");
        waitForPageLoad();
    }

    protected void navigateToRegister() {
        driver.get(baseUrl + "/register");
        waitForPageLoad();
    }

    protected void navigateToDashboard() {
        driver.get(baseUrl + "/dashboard");
        waitForPageLoad();
    }

    protected void navigateToAdminDashboard() {
        driver.get(baseUrl + "/admin/dashboard");
        waitForPageLoad();
    }

    protected void loginAsUser() {
        loginAs(TEST_USER_USERNAME, TEST_USER_PASSWORD);
    }

    protected void loginAsAdmin() {
        loginAs(TEST_ADMIN_USERNAME, TEST_ADMIN_PASSWORD);
    }

    protected void loginAs(String username, String password) {
        navigateToLogin();
        
        WebElement usernameField = wait.until(ExpectedConditions.presenceOfElementLocated(By.name("username")));
        WebElement passwordField = driver.findElement(By.name("password"));
        WebElement loginButton = driver.findElement(By.cssSelector("button[type='submit']"));

        usernameField.clear();
        usernameField.sendKeys(username);
        passwordField.clear();
        passwordField.sendKeys(password);
        loginButton.click();

        // Wait for redirect after login
        waitForPageLoad();
    }

    protected void logout() {
        WebElement logoutLink = wait.until(ExpectedConditions.elementToBeClickable(By.linkText("Logout")));
        logoutLink.click();
        waitForPageLoad();
    }

    protected void waitForPageLoad() {
        await().atMost(Duration.ofSeconds(10))
                .until(() -> driver.getTitle() != null && !driver.getTitle().isEmpty());
    }

    protected void waitForElement(By locator) {
        wait.until(ExpectedConditions.presenceOfElementLocated(locator));
    }

    protected void waitForElementToBeClickable(By locator) {
        wait.until(ExpectedConditions.elementToBeClickable(locator));
    }

    protected void waitForTextToBePresentInElement(By locator, String text) {
        wait.until(ExpectedConditions.textToBePresentInElementLocated(locator, text));
    }

    protected boolean isElementPresent(By locator) {
        try {
            driver.findElement(locator);
            return true;
        } catch (Exception e) {
            return false;
        }
    }

    protected void fillForm(String fieldName, String value) {
        WebElement field = driver.findElement(By.name(fieldName));
        field.clear();
        field.sendKeys(value);
    }

    protected void clickButton(String buttonText) {
        WebElement button = wait.until(ExpectedConditions.elementToBeClickable(By.xpath("//button[contains(text(), '" + buttonText + "')]")));
        button.click();
    }

    protected void clickLink(String linkText) {
        WebElement link = wait.until(ExpectedConditions.elementToBeClickable(By.linkText(linkText)));
        link.click();
    }

    protected String getCurrentUrl() {
        return driver.getCurrentUrl();
    }

    protected String getPageTitle() {
        return driver.getTitle();
    }

    protected boolean isTextPresent(String text) {
        return driver.getPageSource().contains(text);
    }

    protected WebElement findElement(By locator) {
        return wait.until(ExpectedConditions.presenceOfElementLocated(locator));
    }

    protected void selectDropdownOption(By dropdownLocator, String optionText) {
        WebElement dropdown = findElement(dropdownLocator);
        dropdown.click();
        WebElement option = wait.until(ExpectedConditions.elementToBeClickable(
                By.xpath("//option[text()='" + optionText + "']")));
        option.click();
    }
}
