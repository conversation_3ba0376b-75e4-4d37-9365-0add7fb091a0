package com.talent.hunt.ui;

import org.junit.jupiter.api.Test;
import org.openqa.selenium.By;
import org.openqa.selenium.WebElement;
import org.openqa.selenium.support.ui.Select;

import static org.assertj.core.api.Assertions.assertThat;

class TestManagementFlowUITest extends BaseUITest {

    @Test
    void shouldNavigateToTestCreationPage() {
        // Given
        loginAsAdmin();

        // When - navigate to test creation (URL may vary)
        driver.get(baseUrl + "/tests/create");
        waitForPageLoad();

        // Then
        assertThat(getCurrentUrl()).contains("/test");
        assertThat(isTextPresent("Create Test") || isTextPresent("New Test")).isTrue();
        
        // Should have test creation form fields
        assertThat(isElementPresent(By.name("title"))).isTrue();
        assertThat(isElementPresent(By.name("description"))).isTrue();
        assertThat(isElementPresent(By.name("durationMinutes"))).isTrue();
        assertThat(isElementPresent(By.name("passingScore"))).isTrue();
    }

    @Test
    void shouldCreateNewTestSuccessfully() {
        // Given
        loginAsAdmin();
        driver.get(baseUrl + "/tests/create");
        waitForPageLoad();

        String testTitle = "UI Test - " + System.currentTimeMillis();
        String testDescription = "This is a test created via UI automation";

        // When
        fillForm("title", testTitle);
        fillForm("description", testDescription);
        fillForm("durationMinutes", "60");
        fillForm("passingScore", "70");
        clickButton("Create Test");

        // Then
        waitForPageLoad();
        assertThat(isTextPresent("Test created successfully") || 
                  isTextPresent("created") || 
                  getCurrentUrl().contains("/questions")).isTrue();
    }

    @Test
    void shouldValidateRequiredFieldsOnTestCreation() {
        // Given
        loginAsAdmin();
        driver.get(baseUrl + "/tests/create");
        waitForPageLoad();

        // When - submit without required fields
        clickButton("Create Test");

        // Then
        waitForPageLoad();
        assertThat(getCurrentUrl()).contains("/test");
        assertThat(isTextPresent("required") || 
                  isTextPresent("cannot be empty") || 
                  isTextPresent("must not be blank")).isTrue();
    }

    @Test
    void shouldNavigateToQuestionCreationAfterTestCreation() {
        // Given
        loginAsAdmin();
        driver.get(baseUrl + "/tests/create");
        waitForPageLoad();

        String testTitle = "Question Test - " + System.currentTimeMillis();

        // When - create a test
        fillForm("title", testTitle);
        fillForm("description", "Test for question creation");
        fillForm("durationMinutes", "45");
        fillForm("passingScore", "60");
        clickButton("Create Test");

        // Then - should navigate to question creation
        waitForPageLoad();
        assertThat(getCurrentUrl().contains("/questions") ||
                  isTextPresent("Add Question") ||
                  isTextPresent("Question")).isTrue();
    }

    @Test
    void shouldAddQuestionToTest() {
        // Given - create a test first
        loginAsAdmin();
        driver.get(baseUrl + "/tests/create");
        waitForPageLoad();

        String testTitle = "Question Addition Test - " + System.currentTimeMillis();
        fillForm("title", testTitle);
        fillForm("description", "Test for adding questions");
        fillForm("durationMinutes", "30");
        fillForm("passingScore", "50");
        clickButton("Create Test");
        waitForPageLoad();

        // When - add a question (if on question creation page)
        if (getCurrentUrl().contains("/questions") || isTextPresent("Add Question")) {
            fillForm("questionText", "What is 2 + 2?");
            
            // Set question type if dropdown exists
            if (isElementPresent(By.name("questionType"))) {
                Select questionType = new Select(driver.findElement(By.name("questionType")));
                questionType.selectByValue("MULTIPLE_CHOICE");
            }
            
            fillForm("points", "10");
            
            // Add answer options
            if (isElementPresent(By.name("answerOptions[0].optionText"))) {
                fillForm("answerOptions[0].optionText", "3");
                fillForm("answerOptions[1].optionText", "4");
                fillForm("answerOptions[2].optionText", "5");
                fillForm("answerOptions[3].optionText", "6");
                
                // Mark correct answer
                if (isElementPresent(By.name("answerOptions[1].isCorrect"))) {
                    WebElement correctAnswer = driver.findElement(By.name("answerOptions[1].isCorrect"));
                    if (!correctAnswer.isSelected()) {
                        correctAnswer.click();
                    }
                }
            }
            
            clickButton("Add Question");
            waitForPageLoad();

            // Then
            assertThat(isTextPresent("Question added successfully") || 
                      isTextPresent("added") || 
                      isTextPresent("What is 2 + 2?")).isTrue();
        }
    }

    @Test
    void shouldValidateQuestionFields() {
        // Given - navigate to question creation
        loginAsAdmin();
        driver.get(baseUrl + "/tests/create");
        waitForPageLoad();

        // Create test first
        fillForm("title", "Validation Test");
        fillForm("description", "Test validation");
        fillForm("durationMinutes", "30");
        fillForm("passingScore", "50");
        clickButton("Create Test");
        waitForPageLoad();

        // When - try to add question without required fields
        if (getCurrentUrl().contains("/questions") || isTextPresent("Add Question")) {
            clickButton("Add Question");
            waitForPageLoad();

            // Then
            assertThat(isTextPresent("required") || 
                      isTextPresent("cannot be empty") || 
                      isTextPresent("Question text")).isTrue();
        }
    }

    @Test
    void shouldShowTestListForAdmin() {
        // Given
        loginAsAdmin();

        // When - navigate to tests list (URL may vary)
        driver.get(baseUrl + "/tests");
        waitForPageLoad();

        // Then
        assertThat(isTextPresent("Tests") || 
                  isTextPresent("All Tests") || 
                  isTextPresent("Test List")).isTrue();
    }

    @Test
    void shouldAllowTestEditing() {
        // Given - create a test first
        loginAsAdmin();
        driver.get(baseUrl + "/tests/create");
        waitForPageLoad();

        String originalTitle = "Editable Test - " + System.currentTimeMillis();
        fillForm("title", originalTitle);
        fillForm("description", "Original description");
        fillForm("durationMinutes", "60");
        fillForm("passingScore", "70");
        clickButton("Create Test");
        waitForPageLoad();

        // When - try to edit the test (if edit functionality exists)
        if (isElementPresent(By.linkText("Edit")) || isElementPresent(By.linkText("Edit Test"))) {
            clickLink("Edit");
            waitForPageLoad();

            // Update the test
            WebElement titleField = driver.findElement(By.name("title"));
            titleField.clear();
            titleField.sendKeys(originalTitle + " - EDITED");
            clickButton("Update Test");
            waitForPageLoad();

            // Then
            assertThat(isTextPresent("updated successfully") || 
                      isTextPresent("EDITED")).isTrue();
        }
    }

    @Test
    void shouldPreventNonAdminFromCreatingTests() {
        // Given
        loginAsUser(); // Regular user, not admin

        // When - try to access test creation
        driver.get(baseUrl + "/tests/create");
        waitForPageLoad();

        // Then - should be denied access
        assertThat(getCurrentUrl().contains("/login") || 
                  isTextPresent("Access Denied") || 
                  isTextPresent("403") ||
                  isTextPresent("Unauthorized")).isTrue();
    }

    @Test
    void shouldShowTestDetails() {
        // Given - create a test first
        loginAsAdmin();
        driver.get(baseUrl + "/tests/create");
        waitForPageLoad();

        String testTitle = "Detail Test - " + System.currentTimeMillis();
        fillForm("title", testTitle);
        fillForm("description", "Test for viewing details");
        fillForm("durationMinutes", "45");
        fillForm("passingScore", "65");
        clickButton("Create Test");
        waitForPageLoad();

        // When - view test details (if available)
        if (isElementPresent(By.linkText("View")) || isElementPresent(By.linkText("Details"))) {
            clickLink("View");
            waitForPageLoad();

            // Then
            assertThat(isTextPresent(testTitle)).isTrue();
            assertThat(isTextPresent("Test for viewing details")).isTrue();
            assertThat(isTextPresent("45") && isTextPresent("minutes")).isTrue();
            assertThat(isTextPresent("65")).isTrue();
        }
    }

    @Test
    void shouldHandleInvalidTestData() {
        // Given
        loginAsAdmin();
        driver.get(baseUrl + "/tests/create");
        waitForPageLoad();

        // When - enter invalid data
        fillForm("title", ""); // Empty title
        fillForm("description", "Valid description");
        fillForm("durationMinutes", "-10"); // Negative duration
        fillForm("passingScore", "150"); // Invalid passing score (>100)
        clickButton("Create Test");

        // Then
        waitForPageLoad();
        assertThat(getCurrentUrl()).contains("/test");
        assertThat(isTextPresent("required") || 
                  isTextPresent("invalid") || 
                  isTextPresent("must be") ||
                  isTextPresent("error")).isTrue();
    }

    @Test
    void shouldShowQuestionListForTest() {
        // Given - create a test with questions
        loginAsAdmin();
        driver.get(baseUrl + "/tests/create");
        waitForPageLoad();

        String testTitle = "Question List Test - " + System.currentTimeMillis();
        fillForm("title", testTitle);
        fillForm("description", "Test with questions");
        fillForm("durationMinutes", "30");
        fillForm("passingScore", "50");
        clickButton("Create Test");
        waitForPageLoad();

        // When - view questions for the test
        if (getCurrentUrl().contains("/questions") || isTextPresent("Questions")) {
            // Then - should show questions section
            assertThat(isTextPresent("Questions") || 
                      isTextPresent("Add Question") || 
                      isTextPresent("No questions")).isTrue();
        }
    }

    @Test
    void shouldAllowMultipleAnswerOptions() {
        // Given - create test and navigate to question creation
        loginAsAdmin();
        driver.get(baseUrl + "/tests/create");
        waitForPageLoad();

        fillForm("title", "Multiple Choice Test");
        fillForm("description", "Test multiple choice");
        fillForm("durationMinutes", "30");
        fillForm("passingScore", "50");
        clickButton("Create Test");
        waitForPageLoad();

        // When - add question with multiple options
        if (getCurrentUrl().contains("/questions") || isTextPresent("Add Question")) {
            fillForm("questionText", "Which of the following are programming languages?");
            
            // Add multiple answer options if form supports it
            if (isElementPresent(By.name("answerOptions[0].optionText"))) {
                fillForm("answerOptions[0].optionText", "Java");
                fillForm("answerOptions[1].optionText", "Python");
                fillForm("answerOptions[2].optionText", "HTML");
                fillForm("answerOptions[3].optionText", "CSS");
                
                // Mark multiple correct answers if supported
                if (isElementPresent(By.name("answerOptions[0].isCorrect"))) {
                    driver.findElement(By.name("answerOptions[0].isCorrect")).click(); // Java
                    driver.findElement(By.name("answerOptions[1].isCorrect")).click(); // Python
                }
            }
            
            fillForm("points", "15");
            clickButton("Add Question");
            waitForPageLoad();

            // Then
            assertThat(isTextPresent("Question added") || 
                      isTextPresent("programming languages")).isTrue();
        }
    }
}
