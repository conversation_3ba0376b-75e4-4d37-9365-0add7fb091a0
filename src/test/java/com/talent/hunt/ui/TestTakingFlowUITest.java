package com.talent.hunt.ui;

import org.junit.jupiter.api.Test;
import org.openqa.selenium.By;
import org.openqa.selenium.WebElement;

import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;

class TestTakingFlowUITest extends BaseUITest {

    @Test
    void shouldDisplayAvailableTestsOnDashboard() {
        // Given
        loginAsUser();

        // When
        navigateToDashboard();

        // Then
        assertThat(isTextPresent("Available Tests") || 
                  isTextPresent("Tests") ||
                  isTextPresent("No tests available")).isTrue();
    }

    @Test
    void shouldStartTestSession() {
        // Given - user is logged in and there are available tests
        loginAsUser();
        navigateToDashboard();

        // When - try to start a test (if available)
        if (isElementPresent(By.linkText("Start Test")) || 
            isElementPresent(By.xpath("//button[contains(text(), 'Start')]"))) {
            
            WebElement startButton = driver.findElement(
                By.xpath("//button[contains(text(), 'Start')] | //a[contains(text(), 'Start')]"));
            startButton.click();
            waitForPageLoad();

            // Then - should navigate to test taking interface
            assertThat(getCurrentUrl().contains("/session") ||
                      getCurrentUrl().contains("/take") ||
                      isTextPresent("Question") ||
                      isTextPresent("Test Session")).isTrue();
        } else {
            // If no tests available, that's also a valid state to test
            assertThat(isTextPresent("No tests available") || 
                      isTextPresent("No active tests")).isTrue();
        }
    }

    @Test
    void shouldDisplayTestInstructions() {
        // Given - user starts a test
        loginAsUser();
        navigateToDashboard();

        // When - start test if available
        if (isElementPresent(By.xpath("//button[contains(text(), 'Start')] | //a[contains(text(), 'Start')]"))) {
            WebElement startButton = driver.findElement(
                By.xpath("//button[contains(text(), 'Start')] | //a[contains(text(), 'Start')]"));
            startButton.click();
            waitForPageLoad();

            // Then - should show test instructions or first question
            assertThat(isTextPresent("Instructions") || 
                      isTextPresent("Question") ||
                      isTextPresent("Test") ||
                      isTextPresent("Duration")).isTrue();
        }
    }

    @Test
    void shouldDisplayQuestionWithAnswerOptions() {
        // Given - user is in a test session
        loginAsUser();
        navigateToDashboard();

        // When - navigate to test taking interface
        if (isElementPresent(By.xpath("//button[contains(text(), 'Start')] | //a[contains(text(), 'Start')]"))) {
            WebElement startButton = driver.findElement(
                By.xpath("//button[contains(text(), 'Start')] | //a[contains(text(), 'Start')]"));
            startButton.click();
            waitForPageLoad();

            // Then - should display question and answer options
            if (isTextPresent("Question")) {
                // Should have answer options (radio buttons or checkboxes)
                List<WebElement> answerOptions = driver.findElements(
                    By.cssSelector("input[type='radio'], input[type='checkbox']"));
                
                if (answerOptions.size() > 0) {
                    assertThat(answerOptions.size()).isGreaterThan(0);
                }
                
                // Should have submit/next button
                assertThat(isElementPresent(By.xpath("//button[contains(text(), 'Submit')] | " +
                                                   "//button[contains(text(), 'Next')] | " +
                                                   "//button[contains(text(), 'Answer')]"))).isTrue();
            }
        }
    }

    @Test
    void shouldAllowAnswerSelection() {
        // Given - user is in a test with questions
        loginAsUser();
        navigateToDashboard();

        // When - start test and select answer
        if (isElementPresent(By.xpath("//button[contains(text(), 'Start')] | //a[contains(text(), 'Start')]"))) {
            WebElement startButton = driver.findElement(
                By.xpath("//button[contains(text(), 'Start')] | //a[contains(text(), 'Start')]"));
            startButton.click();
            waitForPageLoad();

            // Select an answer option if available
            List<WebElement> answerOptions = driver.findElements(
                By.cssSelector("input[type='radio'], input[type='checkbox']"));
            
            if (answerOptions.size() > 0) {
                answerOptions.get(0).click(); // Select first option
                
                // Submit answer
                if (isElementPresent(By.xpath("//button[contains(text(), 'Submit')] | " +
                                             "//button[contains(text(), 'Next')] | " +
                                             "//button[contains(text(), 'Answer')]"))) {
                    WebElement submitButton = driver.findElement(
                        By.xpath("//button[contains(text(), 'Submit')] | " +
                                "//button[contains(text(), 'Next')] | " +
                                "//button[contains(text(), 'Answer')]"));
                    submitButton.click();
                    waitForPageLoad();

                    // Then - should proceed to next question or show confirmation
                    assertThat(isTextPresent("Question") || 
                              isTextPresent("submitted") || 
                              isTextPresent("Next") ||
                              isTextPresent("Complete")).isTrue();
                }
            }
        }
    }

    @Test
    void shouldShowTestProgress() {
        // Given - user is taking a test
        loginAsUser();
        navigateToDashboard();

        // When - in test session
        if (isElementPresent(By.xpath("//button[contains(text(), 'Start')] | //a[contains(text(), 'Start')]"))) {
            WebElement startButton = driver.findElement(
                By.xpath("//button[contains(text(), 'Start')] | //a[contains(text(), 'Start')]"));
            startButton.click();
            waitForPageLoad();

            // Then - should show progress indicators
            assertThat(isTextPresent("Question") || 
                      isTextPresent("of") || 
                      isTextPresent("Progress") ||
                      isTextPresent("Time") ||
                      isElementPresent(By.cssSelector(".progress, .progress-bar"))).isTrue();
        }
    }

    @Test
    void shouldShowTimeRemaining() {
        // Given - user is in a timed test
        loginAsUser();
        navigateToDashboard();

        // When - start test
        if (isElementPresent(By.xpath("//button[contains(text(), 'Start')] | //a[contains(text(), 'Start')]"))) {
            WebElement startButton = driver.findElement(
                By.xpath("//button[contains(text(), 'Start')] | //a[contains(text(), 'Start')]"));
            startButton.click();
            waitForPageLoad();

            // Then - should show time remaining
            assertThat(isTextPresent("Time") || 
                      isTextPresent("minutes") || 
                      isTextPresent("remaining") ||
                      isTextPresent(":")).isTrue(); // Time format like 45:30
        }
    }

    @Test
    void shouldAllowTestCompletion() {
        // Given - user completes all questions in a test
        loginAsUser();
        navigateToDashboard();

        // When - go through test completion flow
        if (isElementPresent(By.xpath("//button[contains(text(), 'Start')] | //a[contains(text(), 'Start')]"))) {
            WebElement startButton = driver.findElement(
                By.xpath("//button[contains(text(), 'Start')] | //a[contains(text(), 'Start')]"));
            startButton.click();
            waitForPageLoad();

            // Try to complete test (this is a simplified flow)
            if (isElementPresent(By.xpath("//button[contains(text(), 'Complete')] | " +
                                         "//button[contains(text(), 'Finish')] | " +
                                         "//button[contains(text(), 'Submit Test')]"))) {
                WebElement completeButton = driver.findElement(
                    By.xpath("//button[contains(text(), 'Complete')] | " +
                            "//button[contains(text(), 'Finish')] | " +
                            "//button[contains(text(), 'Submit Test')]"));
                completeButton.click();
                waitForPageLoad();

                // Then - should show completion confirmation
                assertThat(isTextPresent("completed") || 
                          isTextPresent("submitted") || 
                          isTextPresent("finished") ||
                          isTextPresent("Thank you")).isTrue();
            }
        }
    }

    @Test
    void shouldShowTestResults() {
        // Given - user has completed a test
        loginAsUser();
        navigateToDashboard();

        // When - check for test results
        if (isTextPresent("Your Test Sessions") || isTextPresent("Test History")) {
            // Look for completed tests or results
            if (isElementPresent(By.linkText("View Results")) || 
                isElementPresent(By.linkText("Results")) ||
                isElementPresent(By.xpath("//a[contains(text(), 'Result')]"))) {
                
                WebElement resultsLink = driver.findElement(
                    By.xpath("//a[contains(text(), 'Result')] | //a[contains(text(), 'View')]"));
                resultsLink.click();
                waitForPageLoad();

                // Then - should show test results
                assertThat(isTextPresent("Score") || 
                          isTextPresent("Result") || 
                          isTextPresent("Points") ||
                          isTextPresent("Correct")).isTrue();
            }
        }
    }

    @Test
    void shouldPreventMultipleActiveSessionsForSameTest() {
        // Given - user starts a test
        loginAsUser();
        navigateToDashboard();

        // When - try to start the same test again
        if (isElementPresent(By.xpath("//button[contains(text(), 'Start')] | //a[contains(text(), 'Start')]"))) {
            WebElement startButton = driver.findElement(
                By.xpath("//button[contains(text(), 'Start')] | //a[contains(text(), 'Start')]"));
            startButton.click();
            waitForPageLoad();

            // Go back to dashboard and try to start again
            navigateToDashboard();
            
            // Then - should either show "Continue Test" or prevent starting new session
            assertThat(isTextPresent("Continue") || 
                      isTextPresent("Active Session") || 
                      isTextPresent("already started") ||
                      !isElementPresent(By.xpath("//button[contains(text(), 'Start')]"))).isTrue();
        }
    }

    @Test
    void shouldAllowContinuingActiveSession() {
        // Given - user has an active test session
        loginAsUser();
        navigateToDashboard();

        // When - start a test
        if (isElementPresent(By.xpath("//button[contains(text(), 'Start')] | //a[contains(text(), 'Start')]"))) {
            WebElement startButton = driver.findElement(
                By.xpath("//button[contains(text(), 'Start')] | //a[contains(text(), 'Start')]"));
            startButton.click();
            waitForPageLoad();

            // Navigate back to dashboard
            navigateToDashboard();

            // Then - should show option to continue active session
            if (isElementPresent(By.linkText("Continue Test")) || 
                isElementPresent(By.linkText("Continue")) ||
                isElementPresent(By.xpath("//a[contains(text(), 'Continue')]"))) {
                
                WebElement continueLink = driver.findElement(
                    By.xpath("//a[contains(text(), 'Continue')]"));
                continueLink.click();
                waitForPageLoad();

                // Should return to test taking interface
                assertThat(isTextPresent("Question") || 
                          isTextPresent("Test Session")).isTrue();
            }
        }
    }

    @Test
    void shouldHandleTestWithNoQuestions() {
        // Given - there might be tests with no questions
        loginAsUser();
        navigateToDashboard();

        // When - try to start a test
        if (isElementPresent(By.xpath("//button[contains(text(), 'Start')] | //a[contains(text(), 'Start')]"))) {
            WebElement startButton = driver.findElement(
                By.xpath("//button[contains(text(), 'Start')] | //a[contains(text(), 'Start')]"));
            startButton.click();
            waitForPageLoad();

            // Then - should handle gracefully
            assertThat(isTextPresent("No questions") || 
                      isTextPresent("Question") || 
                      isTextPresent("Test") ||
                      isTextPresent("Error")).isTrue();
        }
    }

    @Test
    void shouldValidateAnswerSubmission() {
        // Given - user is answering a question
        loginAsUser();
        navigateToDashboard();

        // When - try to submit without selecting answer
        if (isElementPresent(By.xpath("//button[contains(text(), 'Start')] | //a[contains(text(), 'Start')]"))) {
            WebElement startButton = driver.findElement(
                By.xpath("//button[contains(text(), 'Start')] | //a[contains(text(), 'Start')]"));
            startButton.click();
            waitForPageLoad();

            // Try to submit without selecting an answer
            if (isElementPresent(By.xpath("//button[contains(text(), 'Submit')] | " +
                                         "//button[contains(text(), 'Next')] | " +
                                         "//button[contains(text(), 'Answer')]"))) {
                WebElement submitButton = driver.findElement(
                    By.xpath("//button[contains(text(), 'Submit')] | " +
                            "//button[contains(text(), 'Next')] | " +
                            "//button[contains(text(), 'Answer')]"));
                submitButton.click();
                waitForPageLoad();

                // Then - should either require answer selection or allow skipping
                assertThat(isTextPresent("select") || 
                          isTextPresent("required") || 
                          isTextPresent("Question") ||
                          isTextPresent("Next")).isTrue();
            }
        }
    }

    @Test
    void shouldShowQuestionNavigation() {
        // Given - user is in a multi-question test
        loginAsUser();
        navigateToDashboard();

        // When - in test session
        if (isElementPresent(By.xpath("//button[contains(text(), 'Start')] | //a[contains(text(), 'Start')]"))) {
            WebElement startButton = driver.findElement(
                By.xpath("//button[contains(text(), 'Start')] | //a[contains(text(), 'Start')]"));
            startButton.click();
            waitForPageLoad();

            // Then - should show navigation elements
            assertThat(isElementPresent(By.xpath("//button[contains(text(), 'Next')] | " +
                                               "//button[contains(text(), 'Previous')] | " +
                                               "//button[contains(text(), 'Submit')]")) ||
                      isTextPresent("Question")).isTrue();
        }
    }
}
