<!DOCTYPE html>
<html lang="en" xmlns:th="http://www.thymeleaf.org">
<head>
    <title>Register</title>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <main>
        <div class="container">
            <div class="row justify-content-center">
                <div class="col-md-8 col-lg-6">
                    <div class="card shadow mt-4">
                        <div class="card-header bg-success text-white text-center">
                            <h4 class="mb-0">
                                <i class="fas fa-user-plus me-2"></i>Create Account
                            </h4>
                        </div>
                        <div class="card-body">
                            <form th:action="@{/register}" th:object="${userRegistrationDto}" method="post">
                                <!-- CSRF Token -->
                                <input type="hidden" th:if="${_csrf}" th:name="${_csrf.parameterName}" th:value="${_csrf.token}" />

                                <div class="row">
                                    <div class="col-md-6 mb-3">
                                        <label for="firstName" class="form-label">First Name</label>
                                        <input type="text" 
                                               class="form-control" 
                                               th:class="${#fields.hasErrors('firstName')} ? 'form-control is-invalid' : 'form-control'"
                                               id="firstName" 
                                               th:field="*{firstName}" 
                                               placeholder="Enter first name">
                                        <div th:if="${#fields.hasErrors('firstName')}" class="invalid-feedback">
                                            <span th:errors="*{firstName}"></span>
                                        </div>
                                    </div>
                                    
                                    <div class="col-md-6 mb-3">
                                        <label for="lastName" class="form-label">Last Name</label>
                                        <input type="text" 
                                               class="form-control" 
                                               th:class="${#fields.hasErrors('lastName')} ? 'form-control is-invalid' : 'form-control'"
                                               id="lastName" 
                                               th:field="*{lastName}" 
                                               placeholder="Enter last name">
                                        <div th:if="${#fields.hasErrors('lastName')}" class="invalid-feedback">
                                            <span th:errors="*{lastName}"></span>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="mb-3">
                                    <label for="username" class="form-label">Username</label>
                                    <div class="input-group">
                                        <span class="input-group-text">
                                            <i class="fas fa-user"></i>
                                        </span>
                                        <input type="text" 
                                               class="form-control" 
                                               th:class="${#fields.hasErrors('username')} ? 'form-control is-invalid' : 'form-control'"
                                               id="username" 
                                               th:field="*{username}" 
                                               placeholder="Choose a username">
                                    </div>
                                    <div th:if="${#fields.hasErrors('username')}" class="invalid-feedback d-block">
                                        <span th:errors="*{username}"></span>
                                    </div>
                                </div>
                                
                                <div class="mb-3">
                                    <label for="email" class="form-label">Email</label>
                                    <div class="input-group">
                                        <span class="input-group-text">
                                            <i class="fas fa-envelope"></i>
                                        </span>
                                        <input type="email" 
                                               class="form-control" 
                                               th:class="${#fields.hasErrors('email')} ? 'form-control is-invalid' : 'form-control'"
                                               id="email" 
                                               th:field="*{email}" 
                                               placeholder="Enter your email">
                                    </div>
                                    <div th:if="${#fields.hasErrors('email')}" class="invalid-feedback d-block">
                                        <span th:errors="*{email}"></span>
                                    </div>
                                </div>
                                
                                <div class="mb-3">
                                    <label for="password" class="form-label">Password</label>
                                    <div class="input-group">
                                        <span class="input-group-text">
                                            <i class="fas fa-lock"></i>
                                        </span>
                                        <input type="password" 
                                               class="form-control" 
                                               th:class="${#fields.hasErrors('password')} ? 'form-control is-invalid' : 'form-control'"
                                               id="password" 
                                               th:field="*{password}" 
                                               placeholder="Enter password">
                                    </div>
                                    <div th:if="${#fields.hasErrors('password')}" class="invalid-feedback d-block">
                                        <span th:errors="*{password}"></span>
                                    </div>
                                </div>
                                
                                <div class="mb-3">
                                    <label for="confirmPassword" class="form-label">Confirm Password</label>
                                    <div class="input-group">
                                        <span class="input-group-text">
                                            <i class="fas fa-lock"></i>
                                        </span>
                                        <input type="password" 
                                               class="form-control" 
                                               th:class="${#fields.hasErrors('confirmPassword')} ? 'form-control is-invalid' : 'form-control'"
                                               id="confirmPassword" 
                                               th:field="*{confirmPassword}" 
                                               placeholder="Confirm password">
                                    </div>
                                    <div th:if="${#fields.hasErrors('confirmPassword')}" class="invalid-feedback d-block">
                                        <span th:errors="*{confirmPassword}"></span>
                                    </div>
                                </div>
                                
                                <div class="d-grid">
                                    <button type="submit" class="btn btn-success">
                                        <i class="fas fa-user-plus me-2"></i>Create Account
                                    </button>
                                </div>
                            </form>
                        </div>
                        <div class="card-footer text-center">
                            <p class="mb-0">
                                Already have an account? 
                                <a href="/login" class="text-decoration-none">Login here</a>
                            </p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
