#!/bin/bash

# UI Integration Test Runner for Talent Hunt Application
# This script runs the comprehensive UI test suite

echo "🚀 Starting UI Integration Tests for Talent Hunt Application"
echo "============================================================"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if Maven is available
if ! command -v mvn &> /dev/null && ! command -v ./mvnw &> /dev/null; then
    print_error "Maven not found. Please install Maven or use the Maven wrapper (./mvnw)"
    exit 1
fi

# Use Maven wrapper if available, otherwise use system Maven
if [ -f "./mvnw" ]; then
    MVN_CMD="./mvnw"
else
    MVN_CMD="mvn"
fi

print_status "Using Maven command: $MVN_CMD"

# Function to run a specific test class
run_test_class() {
    local test_class=$1
    local description=$2
    
    echo ""
    print_status "Running $description..."
    echo "Test Class: $test_class"
    echo "----------------------------------------"
    
    if $MVN_CMD test -Dtest="$test_class" -q; then
        print_success "$description completed successfully"
        return 0
    else
        print_error "$description failed"
        return 1
    fi
}

# Function to run all UI tests
run_all_ui_tests() {
    echo ""
    print_status "Running All UI Integration Tests..."
    echo "Test Package: com.talent.hunt.ui.*"
    echo "----------------------------------------"
    
    if $MVN_CMD test -Dtest="com.talent.hunt.ui.*" -q; then
        print_success "All UI tests completed successfully"
        return 0
    else
        print_error "Some UI tests failed"
        return 1
    fi
}

# Function to compile the project
compile_project() {
    print_status "Compiling project..."
    if $MVN_CMD clean compile test-compile -q; then
        print_success "Project compiled successfully"
        return 0
    else
        print_error "Project compilation failed"
        return 1
    fi
}

# Main execution
main() {
    local test_option=${1:-"all"}
    local failed_tests=0
    
    # Compile project first
    if ! compile_project; then
        print_error "Cannot proceed with tests due to compilation errors"
        exit 1
    fi
    
    case $test_option in
        "auth")
            run_test_class "AuthenticationFlowUITest" "Authentication Flow Tests"
            failed_tests=$?
            ;;
        "dashboard")
            run_test_class "DashboardNavigationUITest" "Dashboard and Navigation Tests"
            failed_tests=$?
            ;;
        "test-mgmt")
            run_test_class "TestManagementFlowUITest" "Test Management Flow Tests"
            failed_tests=$?
            ;;
        "test-taking")
            run_test_class "TestTakingFlowUITest" "Test Taking Flow Tests"
            failed_tests=$?
            ;;
        "e2e")
            run_test_class "EndToEndWorkflowUITest" "End-to-End Workflow Tests"
            failed_tests=$?
            ;;
        "all")
            print_status "Running individual test suites..."
            
            run_test_class "AuthenticationFlowUITest" "Authentication Flow Tests"
            ((failed_tests += $?))
            
            run_test_class "DashboardNavigationUITest" "Dashboard and Navigation Tests"
            ((failed_tests += $?))
            
            run_test_class "TestManagementFlowUITest" "Test Management Flow Tests"
            ((failed_tests += $?))
            
            run_test_class "TestTakingFlowUITest" "Test Taking Flow Tests"
            ((failed_tests += $?))
            
            run_test_class "EndToEndWorkflowUITest" "End-to-End Workflow Tests"
            ((failed_tests += $?))
            ;;
        "help"|"-h"|"--help")
            echo "Usage: $0 [test-option]"
            echo ""
            echo "Test Options:"
            echo "  all         - Run all UI integration tests (default)"
            echo "  auth        - Run authentication flow tests only"
            echo "  dashboard   - Run dashboard and navigation tests only"
            echo "  test-mgmt   - Run test management flow tests only"
            echo "  test-taking - Run test taking flow tests only"
            echo "  e2e         - Run end-to-end workflow tests only"
            echo "  help        - Show this help message"
            echo ""
            echo "Examples:"
            echo "  $0                    # Run all tests"
            echo "  $0 auth              # Run only authentication tests"
            echo "  $0 e2e               # Run only end-to-end tests"
            exit 0
            ;;
        *)
            print_error "Unknown test option: $test_option"
            print_status "Use '$0 help' to see available options"
            exit 1
            ;;
    esac
    
    # Summary
    echo ""
    echo "============================================================"
    if [ $failed_tests -eq 0 ]; then
        print_success "🎉 All selected tests passed!"
    else
        print_warning "⚠️  Some tests failed or had issues"
        print_status "Check the output above for details"
        print_status "Note: UI tests may fail in environments without proper browser setup"
    fi
    
    echo ""
    print_status "Test Summary:"
    echo "  • Total Test Classes: 5"
    echo "  • Total Test Methods: 63"
    echo "  • Coverage: Authentication, Dashboard, Test Management, Test Taking, E2E"
    echo ""
    print_status "For detailed test information, see: UI_INTEGRATION_TEST_SUMMARY.md"
    
    exit $failed_tests
}

# Run main function with all arguments
main "$@"
